#!/usr/bin/env python3
"""
简单的ICO文件修复脚本
将PNG文件转换为基本的ICO格式
"""

import struct
import os

def create_simple_ico():
    """创建一个简单的ICO文件头，指向PNG数据"""
    
    # 读取现有的PNG数据（如果存在）
    png_path = "appicon.png"
    if not os.path.exists(png_path):
        print(f"PNG文件不存在: {png_path}")
        return False
    
    with open(png_path, 'rb') as f:
        png_data = f.read()
    
    # ICO文件头结构
    # ICONDIR结构 (6 bytes)
    ico_header = struct.pack('<HHH', 
                            0,      # Reserved (must be 0)
                            1,      # Type (1 = ICO)
                            1)      # Number of images
    
    # ICONDIRENTRY结构 (16 bytes)
    png_size = len(png_data)
    ico_entry = struct.pack('<BBBBHHLL',
                           0,           # Width (0 = 256)
                           0,           # Height (0 = 256) 
                           0,           # Color count (0 = no palette)
                           0,           # Reserved
                           1,           # Color planes
                           32,          # Bits per pixel
                           png_size,    # Size of image data
                           22)          # Offset to image data (6 + 16 = 22)
    
    # 写入ICO文件
    ico_path = "windows/icon.ico"
    with open(ico_path, 'wb') as f:
        f.write(ico_header)
        f.write(ico_entry)
        f.write(png_data)
    
    print(f"ICO文件已创建: {ico_path}")
    return True

def create_fallback_ico():
    """创建一个最小的有效ICO文件"""
    
    # 创建一个16x16的简单图标数据
    # 这是一个最小的BMP格式数据
    bmp_data = bytearray([
        # BMP Header (40 bytes)
        40, 0, 0, 0,    # Header size
        16, 0, 0, 0,    # Width
        32, 0, 0, 0,    # Height (16*2 for ICO)
        1, 0,           # Planes
        32, 0,          # Bits per pixel
        0, 0, 0, 0,     # Compression
        0, 4, 0, 0,     # Image size
        0, 0, 0, 0,     # X pixels per meter
        0, 0, 0, 0,     # Y pixels per meter
        0, 0, 0, 0,     # Colors used
        0, 0, 0, 0,     # Important colors
    ])
    
    # 添加16x16像素数据 (每像素4字节 BGRA)
    # 创建一个简单的蓝色渐变图标
    for y in range(16):
        for x in range(16):
            # 计算距离中心的距离
            dx = x - 8
            dy = y - 8
            dist = (dx*dx + dy*dy) ** 0.5
            
            if dist < 7:  # 圆形图标
                # 蓝色渐变
                blue = int(255 * (1 - dist/7))
                green = int(150 * (1 - dist/7))
                red = int(100 * (1 - dist/7))
                alpha = 255
            else:
                # 透明背景
                blue = green = red = alpha = 0
            
            bmp_data.extend([blue, green, red, alpha])
    
    # 添加AND mask (1 bit per pixel, 16x16 = 32 bytes)
    and_mask = bytearray(32)  # 全0表示不透明
    bmp_data.extend(and_mask)
    
    # ICO文件头
    ico_header = struct.pack('<HHH', 0, 1, 1)  # Reserved, Type, Count
    
    # ICO目录项
    ico_entry = struct.pack('<BBBBHHLL',
                           16,                  # Width
                           16,                  # Height
                           0,                   # Colors
                           0,                   # Reserved
                           1,                   # Planes
                           32,                  # Bits per pixel
                           len(bmp_data),       # Size
                           22)                  # Offset
    
    # 写入文件
    ico_path = "windows/icon.ico"
    with open(ico_path, 'wb') as f:
        f.write(ico_header)
        f.write(ico_entry)
        f.write(bmp_data)
    
    print(f"备用ICO文件已创建: {ico_path}")
    return True

if __name__ == "__main__":
    print("正在修复ICO文件...")
    
    # 确保目录存在
    os.makedirs("windows", exist_ok=True)
    
    # 尝试使用PNG创建ICO
    if not create_simple_ico():
        print("PNG方法失败，使用备用方法...")
        create_fallback_ico()
    
    print("完成！")

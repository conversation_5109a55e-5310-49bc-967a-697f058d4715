<!DOCTYPE html>
<html>
<head>
    <title>创建高大上图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
            text-align: center;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin: 20px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <h1>授权管理系统 - 图标生成器</h1>
    <canvas id="iconCanvas" width="512" height="512"></canvas>
    <br>
    <button onclick="generateIcon()">生成图标</button>
    <button onclick="downloadIcon()">下载PNG</button>
    <button onclick="downloadICO()">下载ICO</button>
    
    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 512, 512);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#667eea');
            
            // 绘制主背景圆形
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 添加阴影效果
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 20;
            ctx.shadowOffsetY = 8;
            
            // 绘制内部装饰圆环
            ctx.beginPath();
            ctx.arc(256, 256, 200, 0, 2 * Math.PI);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetY = 0;
            
            // 绘制盾牌形状
            const shieldGradient = ctx.createLinearGradient(160, 80, 352, 400);
            shieldGradient.addColorStop(0, '#4facfe');
            shieldGradient.addColorStop(1, '#00f2fe');
            
            ctx.beginPath();
            ctx.moveTo(256, 80);
            ctx.quadraticCurveTo(200, 80, 160, 160);
            ctx.lineTo(160, 280);
            ctx.quadraticCurveTo(160, 340, 220, 380);
            ctx.lineTo(240, 390);
            ctx.quadraticCurveTo(256, 400, 272, 390);
            ctx.lineTo(292, 380);
            ctx.quadraticCurveTo(352, 340, 352, 280);
            ctx.lineTo(352, 160);
            ctx.quadraticCurveTo(312, 80, 256, 80);
            ctx.closePath();
            
            ctx.fillStyle = shieldGradient;
            ctx.globalAlpha = 0.9;
            ctx.fill();
            ctx.globalAlpha = 1;
            
            // 盾牌边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制锁的主体
            const lockGradient = ctx.createLinearGradient(220, 220, 292, 300);
            lockGradient.addColorStop(0, '#ffecd2');
            lockGradient.addColorStop(1, '#fcb69f');
            
            ctx.beginPath();
            ctx.roundRect(220, 220, 72, 80, 8);
            ctx.fillStyle = lockGradient;
            ctx.fill();
            
            // 锁的边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制锁的圆弧部分
            ctx.beginPath();
            ctx.arc(256, 200, 21, Math.PI, 0, false);
            ctx.strokeStyle = lockGradient;
            ctx.lineWidth = 8;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // 锁的圆弧边框
            ctx.beginPath();
            ctx.arc(256, 200, 21, Math.PI, 0, false);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制锁孔
            ctx.beginPath();
            ctx.arc(256, 250, 8, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fill();
            
            ctx.fillRect(252, 250, 8, 20);
            
            // 添加装饰性光点
            const points = [
                {x: 150, y: 150, r: 4},
                {x: 140, y: 170, r: 2},
                {x: 170, y: 130, r: 3},
                {x: 362, y: 150, r: 4},
                {x: 372, y: 170, r: 2},
                {x: 342, y: 130, r: 3},
                {x: 150, y: 362, r: 4},
                {x: 140, y: 342, r: 2},
                {x: 170, y: 382, r: 3},
                {x: 362, y: 362, r: 4},
                {x: 372, y: 342, r: 2},
                {x: 342, y: 382, r: 3}
            ];
            
            ctx.globalAlpha = 0.3;
            points.forEach(point => {
                ctx.beginPath();
                ctx.arc(point.x, point.y, point.r, 0, 2 * Math.PI);
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.fill();
            });
            ctx.globalAlpha = 1;
            
            // 中心发光点
            ctx.beginPath();
            ctx.arc(256, 256, 6, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fill();
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'appicon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadICO() {
            // 创建多个尺寸的图标
            const sizes = [16, 32, 48, 64, 128, 256];
            const canvases = [];
            
            sizes.forEach(size => {
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = size;
                tempCanvas.height = size;
                const tempCtx = tempCanvas.getContext('2d');
                
                // 缩放绘制原图标
                tempCtx.drawImage(document.getElementById('iconCanvas'), 0, 0, size, size);
                canvases.push(tempCanvas);
            });
            
            // 下载最大尺寸作为ICO（简化版）
            const link = document.createElement('a');
            link.download = 'icon.ico';
            link.href = canvases[canvases.length - 1].toDataURL('image/png');
            link.click();
        }
        
        // 页面加载时生成图标
        window.onload = generateIcon;
    </script>
</body>
</html>

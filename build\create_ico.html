<!DOCTYPE html>
<html>
<head>
    <title>ICO文件生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            margin: 10px;
            background: white;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .size-item {
            text-align: center;
        }
        .size-label {
            margin-top: 10px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 授权管理系统 - ICO图标生成器</h1>
        
        <div class="size-grid">
            <div class="size-item">
                <canvas id="canvas16" width="16" height="16"></canvas>
                <div class="size-label">16x16</div>
            </div>
            <div class="size-item">
                <canvas id="canvas32" width="32" height="32"></canvas>
                <div class="size-label">32x32</div>
            </div>
            <div class="size-item">
                <canvas id="canvas48" width="48" height="48"></canvas>
                <div class="size-label">48x48</div>
            </div>
            <div class="size-item">
                <canvas id="canvas64" width="64" height="64"></canvas>
                <div class="size-label">64x64</div>
            </div>
            <div class="size-item">
                <canvas id="canvas128" width="128" height="128"></canvas>
                <div class="size-label">128x128</div>
            </div>
            <div class="size-item">
                <canvas id="canvas256" width="256" height="256"></canvas>
                <div class="size-label">256x256</div>
            </div>
        </div>
        
        <button onclick="generateAllIcons()">生成所有尺寸图标</button>
        <button onclick="downloadPNG(256)">下载PNG (256x256)</button>
        <button onclick="downloadAllPNG()">下载所有PNG</button>
        
        <div style="margin-top: 20px; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
            <h3>使用说明：</h3>
            <p>1. 点击"生成所有尺寸图标"创建图标</p>
            <p>2. 右键点击任意图标选择"另存为"保存为PNG</p>
            <p>3. 使用在线ICO转换工具将PNG转换为ICO格式</p>
            <p>4. 推荐网站：<a href="https://convertio.co/png-ico/" target="_blank" style="color: #ffecd2;">convertio.co</a></p>
        </div>
    </div>

    <script>
        function drawIcon(ctx, size) {
            const scale = size / 256; // 基于256x256的缩放比例
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置缩放
            ctx.save();
            ctx.scale(scale, scale);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#667eea');
            
            // 绘制主背景圆形
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 绘制盾牌形状
            const shieldGradient = ctx.createLinearGradient(80, 40, 176, 200);
            shieldGradient.addColorStop(0, '#4facfe');
            shieldGradient.addColorStop(1, '#00f2fe');
            
            ctx.beginPath();
            ctx.moveTo(128, 40);
            ctx.quadraticCurveTo(100, 40, 80, 80);
            ctx.lineTo(80, 140);
            ctx.quadraticCurveTo(80, 170, 110, 190);
            ctx.lineTo(120, 195);
            ctx.quadraticCurveTo(128, 200, 136, 195);
            ctx.lineTo(146, 190);
            ctx.quadraticCurveTo(176, 170, 176, 140);
            ctx.lineTo(176, 80);
            ctx.quadraticCurveTo(156, 40, 128, 40);
            ctx.closePath();
            
            ctx.fillStyle = shieldGradient;
            ctx.globalAlpha = 0.9;
            ctx.fill();
            ctx.globalAlpha = 1;
            
            // 盾牌边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制锁的主体
            const lockGradient = ctx.createLinearGradient(110, 110, 146, 150);
            lockGradient.addColorStop(0, '#ffecd2');
            lockGradient.addColorStop(1, '#fcb69f');
            
            ctx.beginPath();
            ctx.roundRect(110, 110, 36, 40, 4);
            ctx.fillStyle = lockGradient;
            ctx.fill();
            
            // 锁的边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // 绘制锁的圆弧部分
            ctx.beginPath();
            ctx.arc(128, 100, 10, Math.PI, 0, false);
            ctx.strokeStyle = lockGradient;
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // 锁的圆弧边框
            ctx.beginPath();
            ctx.arc(128, 100, 10, Math.PI, 0, false);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制锁孔
            ctx.beginPath();
            ctx.arc(128, 125, 4, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fill();
            
            ctx.fillRect(126, 125, 4, 10);
            
            // 如果尺寸足够大，添加装饰点
            if (size >= 64) {
                ctx.globalAlpha = 0.3;
                const points = [
                    {x: 75, y: 75, r: 2},
                    {x: 181, y: 75, r: 2},
                    {x: 75, y: 181, r: 2},
                    {x: 181, y: 181, r: 2}
                ];
                
                points.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, point.r, 0, 2 * Math.PI);
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    ctx.fill();
                });
                ctx.globalAlpha = 1;
            }
            
            ctx.restore();
        }
        
        function generateAllIcons() {
            const sizes = [16, 32, 48, 64, 128, 256];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                drawIcon(ctx, size);
            });
        }
        
        function downloadPNG(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon_${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadAllPNG() {
            const sizes = [16, 32, 48, 64, 128, 256];
            
            sizes.forEach(size => {
                setTimeout(() => {
                    downloadPNG(size);
                }, size * 10); // 延迟下载避免浏览器阻止
            });
        }
        
        // 页面加载时生成图标
        window.onload = generateAllIcons;
    </script>
</body>
</html>

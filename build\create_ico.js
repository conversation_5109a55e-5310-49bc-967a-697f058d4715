// Node.js脚本来创建ICO文件
const fs = require('fs');
const path = require('path');

function createSimpleICO() {
    // 创建一个简单的16x16 ICO文件
    const width = 16;
    const height = 16;
    
    // ICO文件头 (6 bytes)
    const header = Buffer.alloc(6);
    header.writeUInt16LE(0, 0);     // Reserved
    header.writeUInt16LE(1, 2);     // Type (1 = ICO)
    header.writeUInt16LE(1, 4);     // Number of images
    
    // BMP数据大小计算
    const bmpHeaderSize = 40;
    const pixelDataSize = width * height * 4; // 32-bit RGBA
    const andMaskSize = Math.ceil(width * height / 8); // 1 bit per pixel
    const totalBmpSize = bmpHeaderSize + pixelDataSize + andMaskSize;
    
    // ICO目录项 (16 bytes)
    const dirEntry = Buffer.alloc(16);
    dirEntry.writeUInt8(width, 0);      // Width
    dirEntry.writeUInt8(height, 1);     // Height
    dirEntry.writeUInt8(0, 2);          // Color count
    dirEntry.writeUInt8(0, 3);          // Reserved
    dirEntry.writeUInt16LE(1, 4);       // Color planes
    dirEntry.writeUInt16LE(32, 6);      // Bits per pixel
    dirEntry.writeUInt32LE(totalBmpSize, 8);  // Size of image data
    dirEntry.writeUInt32LE(22, 12);     // Offset to image data
    
    // BMP信息头 (40 bytes)
    const bmpHeader = Buffer.alloc(40);
    bmpHeader.writeUInt32LE(40, 0);           // Header size
    bmpHeader.writeInt32LE(width, 4);         // Width
    bmpHeader.writeInt32LE(height * 2, 8);    // Height (doubled for ICO)
    bmpHeader.writeUInt16LE(1, 12);           // Planes
    bmpHeader.writeUInt16LE(32, 14);          // Bits per pixel
    bmpHeader.writeUInt32LE(0, 16);           // Compression
    bmpHeader.writeUInt32LE(pixelDataSize, 20); // Image size
    bmpHeader.writeUInt32LE(0, 24);           // X pixels per meter
    bmpHeader.writeUInt32LE(0, 28);           // Y pixels per meter
    bmpHeader.writeUInt32LE(0, 32);           // Colors used
    bmpHeader.writeUInt32LE(0, 36);           // Important colors
    
    // 创建像素数据 (BGRA格式)
    const pixelData = Buffer.alloc(pixelDataSize);
    let offset = 0;
    
    // 从底部到顶部绘制（BMP格式）
    for (let y = height - 1; y >= 0; y--) {
        for (let x = 0; x < width; x++) {
            // 计算距离中心的距离
            const dx = x - width / 2;
            const dy = y - height / 2;
            const dist = Math.sqrt(dx * dx + dy * dy);
            const maxDist = width / 2;
            
            let b, g, r, a;
            
            if (dist < maxDist - 1) {
                // 创建一个蓝紫色渐变圆形图标
                const intensity = 1 - (dist / maxDist);
                b = Math.floor(234 * intensity); // 蓝色分量
                g = Math.floor(126 * intensity); // 绿色分量  
                r = Math.floor(102 * intensity); // 红色分量
                a = 255; // 不透明
            } else {
                // 透明背景
                b = g = r = a = 0;
            }
            
            pixelData.writeUInt8(b, offset++);
            pixelData.writeUInt8(g, offset++);
            pixelData.writeUInt8(r, offset++);
            pixelData.writeUInt8(a, offset++);
        }
    }
    
    // AND mask (全0表示使用alpha通道)
    const andMask = Buffer.alloc(andMaskSize, 0);
    
    // 组合所有数据
    const icoData = Buffer.concat([
        header,
        dirEntry,
        bmpHeader,
        pixelData,
        andMask
    ]);
    
    // 确保目录存在
    const windowsDir = path.join(__dirname, 'windows');
    if (!fs.existsSync(windowsDir)) {
        fs.mkdirSync(windowsDir, { recursive: true });
    }
    
    // 写入文件
    const icoPath = path.join(windowsDir, 'icon.ico');
    fs.writeFileSync(icoPath, icoData);
    
    console.log(`ICO文件已创建: ${icoPath}`);
    console.log(`文件大小: ${icoData.length} bytes`);
}

// 运行函数
try {
    createSimpleICO();
    console.log('ICO文件创建成功！');
} catch (error) {
    console.error('创建ICO文件时出错:', error);
}

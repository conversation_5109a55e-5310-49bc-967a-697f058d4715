package main

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx context.Context
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called at application startup
func (a *App) startup(ctx context.Context) {
	// Perform your setup here
	a.ctx = ctx
}

// domReady is called after front-end resources have been loaded
func (a App) domReady(ctx context.Context) {
	// 设置窗口大小和位置：居中显示，可拖动
	go func() {
		// 获取屏幕尺寸
		screens, err := runtime.ScreenGetAll(ctx)
		if err == nil && len(screens) > 0 {
			screen := screens[0] // 使用主屏幕

			// 计算窗口位置和大小
			width := 1024
			height := 1000 // 设置一个合适的高度，不占满屏幕
			x := (screen.Width - width) / 2 // 水平居中
			y := (screen.Height - height) / 2 // 垂直居中

			// 只在窗口不是最大化状态时设置大小和位置
			runtime.WindowSetSize(ctx, width, height)
			runtime.WindowSetPosition(ctx, x, y)
		}
	}()
}

// beforeClose is called when the application is about to quit,
// either by clicking the window close button or calling runtime.Quit.
// Returning true will cause the application to continue, false will continue shutdown as normal.
func (a *App) beforeClose(ctx context.Context) (prevent bool) {
	return false
}

// shutdown is called at application termination
func (a *App) shutdown(ctx context.Context) {
	// Perform your teardown here
}

// AuthConfig 授权配置结构体
type AuthConfig struct {
	ServiceCode      string `json:"serviceCode"`
	CustomerCode     string `json:"customerCode"`
	CustomerProtocol string `json:"customerProtocol"`
	AdminPassword    string `json:"adminPassword"`
	AuthDate         string `json:"authDate"`
	CreatedAt        string `json:"createdAt"`
	UpdatedAt        string `json:"updatedAt"`
}

// EncryptedConfig 加密配置结构体
type EncryptedConfig struct {
	EncryptedData string `json:"encryptedData"`
	CreatedAt     string `json:"createdAt"`
	UpdatedAt     string `json:"updatedAt"`
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

// RunConfig run.json配置结构体
type RunConfig struct {
	SecretKey string `json:"secretKey"`
}

// SaveAuthConfig 保存授权配置
func (a *App) SaveAuthConfig(config AuthConfig) (string, error) {
	// 首先验证密钥
	if err := validateSecretKey(); err != nil {
		return "", err
	}

	// 添加时间戳
	now := time.Now().Format("2006-01-02 15:04:05")

	// 生成加密字符串
	encryptedData, err := GetAesString(config.ServiceCode, config.CustomerCode, config.CustomerProtocol, config.AuthDate,config.AdminPassword)
	if err != nil {
		return "", fmt.Errorf("生成加密数据失败: %v", err)
	}

	// 创建加密配置对象
	encryptedConfig := EncryptedConfig{
		EncryptedData: encryptedData,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 获取配置文件路径
	configPath, err := getConfigPath()
	if err != nil {
		return "", fmt.Errorf("获取配置路径失败: %v", err)
	}

	// 将加密配置转换为JSON
	configJSON, err := json.MarshalIndent(encryptedConfig, "", "  ")
	if err != nil {
		return "", fmt.Errorf("配置序列化失败: %v", err)
	}

	// 写入配置文件（直接写入程序目录）
	if err := os.WriteFile(configPath, configJSON, 0644); err != nil {
		return "", fmt.Errorf("写入配置文件失败: %v", err)
	}

	return fmt.Sprintf("配置已成功保存到: %s\n加密数据: %s", configPath, encryptedData), nil
}

// LoadAuthConfig 加载授权配置
func (a *App) LoadAuthConfig() (*EncryptedConfig, error) {
	configPath, err := getConfigPath()
	if err != nil {
		return nil, fmt.Errorf("获取配置路径失败: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在")
	}

	// 读取配置文件
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析JSON（现在是加密配置）
	var config EncryptedConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// getConfigPath 获取配置文件路径
func getConfigPath() (string, error) {
	// 获取程序启动的目录
	exePath, err := os.Executable()
	if err != nil {
		return "", err
	}

	// 获取程序所在目录
	exeDir := filepath.Dir(exePath)

	// 配置文件路径（在程序同目录下）
	configPath := filepath.Join(exeDir, "auth_config.json")

	return configPath, nil
}

// GetConfigPath 获取配置文件路径（供前端调用）
func (a *App) GetConfigPath() (string, error) {
	return getConfigPath()
}

// MinimizeWindow 最小化窗口
func (a *App) MinimizeWindow() {
	runtime.WindowMinimise(a.ctx)
}

// CloseWindow 关闭窗口
func (a *App) CloseWindow() {
	runtime.Quit(a.ctx)
}

// GetDeviceKey 获取设备识别码（供前端调用）
func (a *App) GetDeviceKey() string {
	return GetDeviceKey()
}

// MaximizeWindow 最大化窗口到全屏
func (a *App) MaximizeWindow() {
	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err == nil && len(screens) > 0 {
		screen := screens[0] // 使用主屏幕

		// 设置窗口大小为屏幕大小
		runtime.WindowSetSize(a.ctx, screen.Width, screen.Height)
		runtime.WindowSetPosition(a.ctx, 0, 0)

		// 也可以使用Wails的最大化功能
		runtime.WindowMaximise(a.ctx)
	}
}

// validateSecretKey 验证密钥
func validateSecretKey() error {
	// 获取run.json文件路径
	runConfigPath, err := getRunConfigPath()
	if err != nil {
		return fmt.Errorf("获取run.json路径失败: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(runConfigPath); os.IsNotExist(err) {
		return fmt.Errorf("密钥文件run.json不存在，请联系管理员")
	}

	// 读取run.json文件
	runConfigData, err := os.ReadFile(runConfigPath)
	if err != nil {
		return fmt.Errorf("读取密钥文件失败，请联系管理员")
	}

	// 解析JSON
	var runConfig RunConfig
	if err := json.Unmarshal(runConfigData, &runConfig); err != nil {
		return fmt.Errorf("解析密钥文件失败，请联系管理员")
	}

	// 检查密钥字段是否存在且不为空
	if runConfig.SecretKey == "" {	
		return fmt.Errorf("密钥文件run.json中密钥字段不存在，请联系管理员")
	}

	// 对SecretKey进行字符交换和AES解密
	decryptedSecretKey, err := decryptSecretKey(runConfig.SecretKey)
	if err != nil {
		return fmt.Errorf("密钥解密失败，请联系管理员")
	}

	var deviceKey = GetDeviceKey()
	if !strings.Contains(decryptedSecretKey, deviceKey) {
		return fmt.Errorf("密钥不正确，请联系管理员")
	}

	return nil
}

// getRunConfigPath 获取run.json文件路径
func getRunConfigPath() (string, error) {
	// 获取程序启动的目录
	exePath, err := os.Executable()
	if err != nil {
		return "", err
	}

	// 获取程序所在目录
	exeDir := filepath.Dir(exePath)

	// run.json文件路径（在程序同目录下）
	runConfigPath := filepath.Join(exeDir, "run.json")

	return runConfigPath, nil
}

// AESHelper AES加密辅助函数
func AesEncrypt(plaintext, key, iv string) (string, error) {
	// 确保key和iv长度为16字节
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	if len(keyBytes) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}
	if len(ivBytes) != 16 {
		return "", fmt.Errorf("IV长度必须为16字节")
	}

	// 创建AES加密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS7填充
	plaintextBytes := pkcs7Padding([]byte(plaintext), aes.BlockSize)

	// 使用CBC模式加密
	mode := cipher.NewCBCEncrypter(block, ivBytes)
	ciphertext := make([]byte, len(plaintextBytes))
	mode.CryptBlocks(ciphertext, plaintextBytes)

	// 返回Base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// pkcs7Padding PKCS7填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// GetAesString 生成AES加密字符串
func GetAesString(serviceCode, customerCode, customerProtocol, authDate,password string) (string, error) {
	// 构建JSON字符串，参考C#代码格式
	jsonStr := fmt.Sprintf(`{"code":"%s","iXieYi":"%s","iType":"%s","iSonType":"%s","time":"%s","pwd":"%s"}`,
		serviceCode, customerProtocol, serviceCode, customerCode, authDate,password)

	// 使用固定的密钥和IV
	key := "jAKSMXWzPPfwLrt1"
	iv := "QmgU18iFkPQCLrt2"

	// AES加密
	resultStr, err := AesEncrypt(jsonStr, key, iv)
	if err != nil {
		return "", err
	}

	// 交换第5位和倒数第5位字符
	if len(resultStr) >= 10 { // 确保字符串长度足够
		charArray := []rune(resultStr)
		temp := charArray[4] // 第5位（索引4）
		charArray[4] = charArray[len(charArray)-5] // 倒数第5位
		charArray[len(charArray)-5] = temp
		resultStr = string(charArray)
	}

	return resultStr, nil
}

// AesDecrypt AES解密函数
func AesDecrypt(ciphertext, key, iv string) (string, error) {
	// 确保key和iv长度为16字节
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	if len(keyBytes) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}
	if len(ivBytes) != 16 {
		return "", fmt.Errorf("IV长度必须为16字节")
	}

	// Base64解码
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建AES解密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	// 使用CBC模式解密
	mode := cipher.NewCBCDecrypter(block, ivBytes)
	plaintext := make([]byte, len(ciphertextBytes))
	mode.CryptBlocks(plaintext, ciphertextBytes)

	// 移除PKCS7填充
	plaintext = pkcs7UnPadding(plaintext)

	return string(plaintext), nil
}

// pkcs7UnPadding 移除PKCS7填充
func pkcs7UnPadding(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return data
	}

	return data[:len(data)-padding]
}

// decryptSecretKey 解密SecretKey：先交换字符，再AES解密
func decryptSecretKey(encryptedKey string) (string, error) {
	// 先进行字符交换（恢复第5位和倒数第5位字符的交换）
	if len(encryptedKey) >= 10 { // 确保字符串长度足够
		charArray := []rune(encryptedKey)
		// 交换回来：第5位和倒数第5位
		temp := charArray[4] // 第5位（索引4）
		charArray[4] = charArray[len(charArray)-5] // 倒数第5位
		charArray[len(charArray)-5] = temp
		encryptedKey = string(charArray)
	}

	// 使用固定的密钥和IV进行AES解密
	key := "jAKSMXWzPPfwLRt3"
	iv := "QmgU18iFkPQCLRt4"

	// AES解密
	decryptedData, err := AesDecrypt(encryptedKey, key, iv)
	if err != nil {
		return "", err
	}

	return decryptedData, nil
}

// 获取当前设备的网络MAC地址加密后的KEY
func GetDeviceKey() string {

	//获取mac地址
	info, err := GetMACAddress()
	if err != nil {
		return ""
	}

	//加密 - 使用MAC地址作为所有参数
	result, err := GetAesString(string(info), string(info), string(info), string(info), string(info))
	if err != nil {
		return ""
	}

	//MD5
	h := md5.New()
	io.WriteString(h, result)
	md5Str := fmt.Sprintf("%x", h.Sum(nil))
	key := strings.ToLower(md5Str)
	return key
}

// 获取MAC地址
func GetMACAddress() (string, error) {

	info := ""
	interfaces, err := net.Interfaces()
	if err != nil {
		return info, err
	}

	var macAddr string
	for _, iface := range interfaces {
		if iface.HardwareAddr != nil {
			macAddr = iface.HardwareAddr.String()
			break
		}
	}

	return macAddr, nil
}

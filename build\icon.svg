<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- 内部装饰圆环 -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="256" cy="256" r="180" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- 盾牌形状 -->
  <path d="M256 80 
           C 200 80, 160 120, 160 160
           L 160 280
           C 160 320, 180 360, 220 380
           L 240 390
           C 248 394, 264 394, 272 390
           L 292 380
           C 332 360, 352 320, 352 280
           L 352 160
           C 352 120, 312 80, 256 80 Z" 
        fill="url(#shieldGradient)" 
        filter="url(#glow)" 
        opacity="0.9"/>
  
  <!-- 盾牌边框 -->
  <path d="M256 80 
           C 200 80, 160 120, 160 160
           L 160 280
           C 160 320, 180 360, 220 380
           L 240 390
           C 248 394, 264 394, 272 390
           L 292 380
           C 332 360, 352 320, 352 280
           L 352 160
           C 352 120, 312 80, 256 80 Z" 
        fill="none" 
        stroke="rgba(255,255,255,0.6)" 
        stroke-width="3"/>
  
  <!-- 锁的主体 -->
  <rect x="220" y="220" width="72" height="80" rx="8" ry="8" 
        fill="url(#lockGradient)" 
        filter="url(#glow)"/>
  
  <!-- 锁的边框 -->
  <rect x="220" y="220" width="72" height="80" rx="8" ry="8" 
        fill="none" 
        stroke="rgba(255,255,255,0.8)" 
        stroke-width="2"/>
  
  <!-- 锁的圆弧部分 -->
  <path d="M 235 220 
           C 235 200, 245 180, 256 180
           C 267 180, 277 200, 277 220" 
        fill="none" 
        stroke="url(#lockGradient)" 
        stroke-width="8" 
        stroke-linecap="round"/>
  
  <!-- 锁的圆弧边框 -->
  <path d="M 235 220 
           C 235 200, 245 180, 256 180
           C 267 180, 277 200, 277 220" 
        fill="none" 
        stroke="rgba(255,255,255,0.8)" 
        stroke-width="3" 
        stroke-linecap="round"/>
  
  <!-- 锁孔 -->
  <circle cx="256" cy="250" r="8" fill="rgba(255,255,255,0.9)"/>
  <rect x="252" y="250" width="8" height="20" fill="rgba(255,255,255,0.9)"/>
  
  <!-- 装饰性几何元素 -->
  <g opacity="0.3">
    <!-- 左上角装饰 -->
    <circle cx="150" cy="150" r="4" fill="rgba(255,255,255,0.6)"/>
    <circle cx="140" cy="170" r="2" fill="rgba(255,255,255,0.4)"/>
    <circle cx="170" cy="130" r="3" fill="rgba(255,255,255,0.5)"/>
    
    <!-- 右上角装饰 -->
    <circle cx="362" cy="150" r="4" fill="rgba(255,255,255,0.6)"/>
    <circle cx="372" cy="170" r="2" fill="rgba(255,255,255,0.4)"/>
    <circle cx="342" cy="130" r="3" fill="rgba(255,255,255,0.5)"/>
    
    <!-- 左下角装饰 -->
    <circle cx="150" cy="362" r="4" fill="rgba(255,255,255,0.6)"/>
    <circle cx="140" cy="342" r="2" fill="rgba(255,255,255,0.4)"/>
    <circle cx="170" cy="382" r="3" fill="rgba(255,255,255,0.5)"/>
    
    <!-- 右下角装饰 -->
    <circle cx="362" cy="362" r="4" fill="rgba(255,255,255,0.6)"/>
    <circle cx="372" cy="342" r="2" fill="rgba(255,255,255,0.4)"/>
    <circle cx="342" cy="382" r="3" fill="rgba(255,255,255,0.5)"/>
  </g>
  
  <!-- 中心发光点 -->
  <circle cx="256" cy="256" r="6" fill="rgba(255,255,255,0.8)" filter="url(#glow)"/>
</svg>

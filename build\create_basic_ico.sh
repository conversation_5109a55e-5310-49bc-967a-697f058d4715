#!/bin/bash

# 创建一个基本的ICO文件
# 这个脚本创建一个最小的有效ICO文件

echo "创建基本ICO文件..."

# 确保windows目录存在
mkdir -p windows

# 创建一个16x16的基本ICO文件
# 使用printf创建二进制数据

# ICO文件头 (6 bytes)
# Reserved (2 bytes) + Type (2 bytes) + Count (2 bytes)
printf '\x00\x00\x01\x00\x01\x00' > windows/icon.ico

# ICO目录项 (16 bytes)
# Width(1) + Height(1) + Colors(1) + Reserved(1) + Planes(2) + BitCount(2) + Size(4) + Offset(4)
printf '\x10\x10\x00\x00\x01\x00\x20\x00\x00\x04\x00\x00\x16\x00\x00\x00' >> windows/icon.ico

# BMP信息头 (40 bytes)
printf '\x28\x00\x00\x00\x10\x00\x00\x00\x20\x00\x00\x00\x01\x00\x20\x00' >> windows/icon.ico
printf '\x00\x00\x00\x00\x00\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00' >> windows/icon.ico
printf '\x00\x00\x00\x00\x00\x00\x00\x00' >> windows/icon.ico

# 创建16x16像素数据 (1024 bytes, 每像素4字节 BGRA)
# 创建一个简单的蓝色圆形图标
for ((y=0; y<16; y++)); do
    for ((x=0; x<16; x++)); do
        # 计算距离中心的距离
        dx=$((x - 8))
        dy=$((y - 8))
        dist=$((dx*dx + dy*dy))
        
        if [ $dist -lt 49 ]; then  # 半径7的圆
            # 蓝紫色像素 (BGRA格式)
            printf '\xea\x7e\x66\xff' >> windows/icon.ico
        else
            # 透明像素
            printf '\x00\x00\x00\x00' >> windows/icon.ico
        fi
    done
done

# AND mask (32 bytes, 全0表示使用alpha通道)
for ((i=0; i<32; i++)); do
    printf '\x00' >> windows/icon.ico
done

echo "ICO文件已创建: windows/icon.ico"
echo "文件大小: $(wc -c < windows/icon.ico) bytes"

# 验证文件
if [ -f windows/icon.ico ]; then
    echo "✓ ICO文件创建成功"
    file windows/icon.ico
else
    echo "✗ ICO文件创建失败"
fi
